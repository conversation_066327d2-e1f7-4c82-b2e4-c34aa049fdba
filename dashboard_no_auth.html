<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="AstrBot Soulter" />
    <meta name="description" content="AstrBot Dashboard" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
    />
    <title>AstrBot - 仪表盘（集成模式）</title>
    <script type="module" crossorigin src="/assets/index-c41ea1c5.js"></script>
    <link rel="stylesheet" href="/assets/index-c3397e00.css">

    <!-- 集成模式：自动设置假token绕过认证 -->
    <script>
      // 在Vue应用加载前设置假token
      localStorage.setItem('token', 'integration-mode-fake-token');
      localStorage.setItem('username', 'integration-user');

      // 监听页面加载完成
      document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行，确保Vue应用已初始化
        setTimeout(function() {
          // 如果当前在登录页面，自动跳转到仪表盘
          if (window.location.hash === '#/auth/login' || window.location.hash === '' || window.location.hash === '#/') {
            window.location.hash = '#/main/dashboard/default';
          }
        }, 100);
      });

      // 拦截可能的认证检查
      window.addEventListener('load', function() {
        // 确保token始终存在
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'integration-mode-fake-token');
          localStorage.setItem('username', 'integration-user');
        }
      });
    </script>
  </head>
  <body>
    <div id="app"></div>

  </body>
</html>
