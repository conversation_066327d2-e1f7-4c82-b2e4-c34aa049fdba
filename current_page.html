<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="AstrBot Soulter" />
    <meta name="description" content="AstrBot Dashboard" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
    />
    <title>AstrBot - 仪表盘（集成模式 - 测试版）</title>
    <script type="module" crossorigin src="/assets/index-1241ccfb.js"></script>
    <link rel="stylesheet" href="/assets/index-c3397e00.css">

    <!-- 集成模式：自动设置假token绕过认证 -->
    <script>
      console.log('🔗 AstrBot集成模式：初始化无认证访问（测试版）');

      // 在Vue应用加载前设置假token
      localStorage.setItem('token', 'integration-mode-fake-token');
      localStorage.setItem('username', 'integration-user');
      localStorage.setItem('user', 'integration-user');

      // 调试信息
      console.log('Token设置完成:', localStorage.getItem('token'));
      console.log('当前URL:', window.location.href);
      console.log('当前Hash:', window.location.hash);

      // 确保token始终存在
      setInterval(function() {
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'integration-mode-fake-token');
          localStorage.setItem('username', 'integration-user');
          localStorage.setItem('user', 'integration-user');
        }
      }, 1000);
    </script>
  </head>
  <body>
    <div id="app"></div>

  </body>
</html>
