services:
  adapters:
    container_name: maim-bot-adapters
    #### prod ####
    image: unclas/maimbot-adapter:latest
    # image: infinitycat/maimbot-adapter:latest
    #### dev ####
    # image: unclas/maimbot-adapter:dev
    # image: infinitycat/maimbot-adapter:dev
    environment:
      - TZ=Asia/Shanghai
#    ports:
#      - "8095:8095"
    volumes:
      - ./docker-config/adapters/config.toml:/adapters/config.toml # 持久化adapters配置文件
      - ./data/adapters:/adapters/data # adapters 数据持久化
    restart: always
    networks:
      - maim_bot

  core:
    container_name: maim-bot-core
    #### prod ####
    image: sengokucola/maibot:latest
    # image: infinitycat/maibot:latest
    #### dev ####
    # image: sengokucola/maibot:dev
    # image: infinitycat/maibot:dev
    environment:
      - TZ=Asia/Shanghai
#      - EULA_AGREE=bda99dca873f5d8044e9987eac417e01  # 同意EULA
#      - PRIVACY_AGREE=42dddb3cbe2b784b45a2781407b298a1 # 同意EULA
#    ports:
#      - "8000:8000"
    volumes:
      - ./docker-config/mmc/.env:/MaiMBot/.env # 持久化env配置文件
      - ./docker-config/mmc:/MaiMBot/config # 持久化bot配置文件
      - ./data/MaiMBot/maibot_statistics.html:/MaiMBot/maibot_statistics.html #统计数据输出
      - ./data/MaiMBot:/MaiMBot/data # 共享目录
    restart: always
    networks:
      - maim_bot

  napcat:
    environment:
      - NAPCAT_UID=1000
      - NAPCAT_GID=1000
      - TZ=Asia/Shanghai
    ports:
      - "6099:6099"
    volumes:
      - ./docker-config/napcat:/app/napcat/config # 持久化napcat配置文件
      - ./data/qq:/app/.config/QQ # 持久化QQ本体
      - ./data/MaiMBot:/MaiMBot/data # 共享目录
    container_name: maim-bot-napcat
    restart: always
    image: mlikiowa/napcat-docker:latest
    networks:
      - maim_bot

  sqlite-web:
    image: coleifer/sqlite-web
    container_name: sqlite-web
    restart: always
    ports:
      - "8120:8080"
    volumes:
      - ./data/MaiMBot:/data/MaiMBot
    environment:
      - SQLITE_DATABASE=MaiMBot/MaiBot.db  # 你的数据库文件
    networks:
      - maim_bot
      
networks:
  maim_bot:
    driver: bridge
