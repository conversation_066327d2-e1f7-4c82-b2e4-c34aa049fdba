# -*- coding: utf-8 -*-
"""
IPC客户端（WebSocket）- 第1部分≤150行

功能：
- 连接AstrBot IPC服务端（ws://127.0.0.1:8081/ws）
- 心跳检测（定期发送ping）
- 自动重连（指数退避）
- 基础API：发送消息、查询状态
"""
from __future__ import annotations

import asyncio
import json
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

from src.common.logger import get_logger

try:
    import websockets  # type: ignore
except Exception as e:  # noqa: BLE001
    raise RuntimeError("缺少 websockets 依赖，请安装 websockets>=10.0") from e

from .utils import 加载并验证配置, 配置错误


@dataclass
class IPC配置:
    url: str
    连接超时: int = 10
    心跳间隔: int = 30
    请求超时: int = 30
    最大重连次数: int = 10


class AstrBotIPC客户端:
    """AstrBot IPC WebSocket 客户端。"""

    def __init__(self, 插件根: Path) -> None:
        self._root = 插件根
        self._logger = get_logger("AstrBotIPC")
        cfg = 加载并验证配置(
            self._root / "config.json",
            {"ipc_settings.host": str, "ipc_settings.port": int},
        ).数据
        host = cfg["ipc_settings"]["host"]
        port = cfg["ipc_settings"]["port"]
        self._cfg = IPC配置(url=f"ws://{host}:{port}/ws")
        self._ws: Optional[websockets.WebSocketClientProtocol] = None  # type: ignore
        self._stop = False
        self._connected_evt = asyncio.Event()

    async def 连接(self) -> None:
        """建立连接并启动心跳与接收循环。"""
        尝试 = 0
        等待 = 1.0
        while not self._stop:
            try:
                self._logger.info(f"正在连接 AstrBot IPC: {self._cfg.url}")
                self._ws = await asyncio.wait_for(websockets.connect(self._cfg.url), timeout=self._cfg.连接超时)  # type: ignore
                self._connected_evt.set()
                self._logger.info("AstrBot IPC 连接成功")
                # 并行任务：心跳 + 接收
                await asyncio.gather(self._心跳循环(), self._接收循环())
            except Exception as e:  # noqa: BLE001
                self._connected_evt.clear()
                self._logger.error(f"IPC连接失败: {e}")
                尝试 += 1
                if 尝试 > self._cfg.最大重连次数:
                    self._logger.error("达到最大重连次数，停止重连")
                    break
                await asyncio.sleep(等待)
                等待 = min(等待 * 2, 30.0)
            finally:
                if self._ws:
                    try:
                        await self._ws.close()  # type: ignore
                    except Exception:  # noqa: BLE001
                        pass
                    self._ws = None

    async def _心跳循环(self) -> None:
        while not self._stop and self._ws:
            try:
                await asyncio.sleep(self._cfg.心跳间隔)
                await self._发送({"type": "ping", "ts": int(time.time())})
            except Exception as e:  # noqa: BLE001
                self._logger.warning(f"心跳异常: {e}")
                break

    async def _接收循环(self) -> None:
        assert self._ws is not None
        while not self._stop:
            try:
                msg = await self._ws.recv()  # type: ignore
                self._处理消息(msg)
            except Exception as e:  # noqa: BLE001
                self._logger.warning(f"接收异常: {e}")
                break

    def _处理消息(self, 原始: Any) -> None:
        try:
            data = json.loads(原始)
        except Exception:
            self._logger.debug(f"收到非JSON消息: {原始!r}")
            return
        mtype = data.get("type")
        if mtype == "pong":
            self._logger.debug("收到心跳响应 pong")
        else:
            self._logger.debug(f"收到消息: {data}")

    async def _发送(self, 数据: Dict[str, Any]) -> None:
        if not self._ws:
            raise RuntimeError("WebSocket 未连接")
        await asyncio.wait_for(self._ws.send(json.dumps(数据)), timeout=self._cfg.请求超时)  # type: ignore

    # ========== 对外API ==========

    async def 发送Astr消息(self, 会话: str, 文本: str) -> Dict[str, Any]:
        """发送文本消息到AstrBot。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "send_message",
            "data": {"session": 会话, "text": 文本}
        })
        return {"status": "ok"}

    async def 发送图片(self, 会话: str, 图片路径: str) -> Dict[str, Any]:
        """发送图片消息到AstrBot。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "send_image",
            "data": {"session": 会话, "image_path": 图片路径}
        })
        return {"status": "ok"}

    async def 发送语音(self, 会话: str, 语音路径: str) -> Dict[str, Any]:
        """发送语音消息到AstrBot。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "send_voice",
            "data": {"session": 会话, "voice_path": 语音路径}
        })
        return {"status": "ok"}

    async def 查询状态(self) -> Dict[str, Any]:
        """查询AstrBot运行状态。"""
        await self._connected_evt.wait()
        await self._发送({"type": "status"})
        return {"status": "ok"}

    async def 获取配置(self, 节: str, 键: str) -> Dict[str, Any]:
        """获取AstrBot配置项。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "get_config",
            "data": {"section": 节, "key": 键}
        })
        return {"status": "ok"}

    async def 设置配置(self, 节: str, 键: str, 值: Any) -> Dict[str, Any]:
        """设置AstrBot配置项。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "set_config",
            "data": {"section": 节, "key": 键, "value": 值}
        })
        return {"status": "ok"}

    async def 列出插件(self) -> Dict[str, Any]:
        """列出AstrBot所有插件。"""
        await self._connected_evt.wait()
        await self._发送({"type": "list_plugins"})
        return {"status": "ok"}

    async def 启用插件(self, 插件名: str) -> Dict[str, Any]:
        """启用指定插件。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "enable_plugin",
            "data": {"plugin_name": 插件名}
        })
        return {"status": "ok"}

    async def 禁用插件(self, 插件名: str) -> Dict[str, Any]:
        """禁用指定插件。"""
        await self._connected_evt.wait()
        await self._发送({
            "type": "disable_plugin",
            "data": {"plugin_name": 插件名}
        })
        return {"status": "ok"}

    async def 获取系统状态(self) -> Dict[str, Any]:
        """获取系统状态信息。"""
        await self._connected_evt.wait()
        await self._发送({"type": "system_status"})
        return {"status": "ok"}

    async def 获取性能指标(self) -> Dict[str, Any]:
        """获取性能监控指标。"""
        await self._connected_evt.wait()
        await self._发送({"type": "performance_metrics"})
        return {"status": "ok"}

    async def 停止(self) -> None:
        """停止IPC客户端。"""
        self._stop = True
        if self._ws:
            try:
                await self._ws.close()  # type: ignore
            except Exception:  # noqa: BLE001
                pass

