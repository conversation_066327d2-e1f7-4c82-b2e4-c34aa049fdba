# -*- coding: utf-8 -*-
"""
性能优化器（企业级骨架）
- 性能指标收集（QPS、延迟、内存、CPU）
- 简易观测点装饰器
- 基于窗口的统计（滑动窗口）
"""
from __future__ import annotations

import asyncio
import statistics
import time
from collections import deque
from dataclasses import dataclass
from typing import Callable, Deque, Optional

from src.common.logger import get_logger


@dataclass
class 指标快照:
    时间戳: float
    延迟ms: float


class 性能优化器:
    def __init__(self, 窗口秒: int = 60) -> None:
        self._logger = get_logger("PerfOpt")
        self._window_sec = 窗口秒
        self._latencies: Deque[指标快照] = deque(maxlen=5000)

    def 观测点(self, 名称: str) -> Callable:
        def 装饰器(fn: Callable):
            async def 包装(*args, **kwargs):
                开 = time.perf_counter()
                结果 = await fn(*args, **kwargs)
                耗 = (time.perf_counter() - 开) * 1000
                self._latencies.append(指标快照(time.time(), 耗))
                return 结果
            return 包装
        return 装饰器

    def 汇总(self) -> dict:
        截止 = time.time() - self._window_sec
        数据 = [x.延迟ms for x in self._latencies if x.时间戳 >= 截止]
        if not 数据:
            return {"count": 0}
        return {
            "count": len(数据),
            "avg_ms": statistics.fmean(数据),
            "p95_ms": sorted(数据)[int(len(数据) * 0.95) - 1],
            "max_ms": max(数据),
        }

