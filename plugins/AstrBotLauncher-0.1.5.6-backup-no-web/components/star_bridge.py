# -*- coding: utf-8 -*-
"""
Star桥接器（企业级骨架实现 - 第1部分≤150行）

职责：
- 扫描AstrBot Star插件目录，识别插件类与@filter装饰器
- 将Star的command/handler映射为MaiBot Command/Action组件的元信息
- 生命周期管理：加载、卸载、热重载（骨架）

约束：
- 仅实现骨架与关键流程；后续用 str-replace-editor 追加实现
- 全中文注释与UTF-8编码
"""
from __future__ import annotations

import asyncio
import importlib
import inspect
import json
import os
from dataclasses import dataclass
from pathlib import Path
from types import ModuleType
from typing import Any, Dict, List, Optional, Tuple, Type

from src.common.logger import get_logger
from src.plugin_system.base.base_action import BaseAction
from src.plugin_system.base.base_command import BaseCommand
from src.plugin_system.base.component_types import (
    ActionInfo,
    CommandInfo,
    ComponentType,
    ComponentInfo,
)
from src.plugin_system.core.component_registry import component_registry

from .utils import 初始化日志, 配置错误, 加载并验证配置


@dataclass
class Star入口:
    模块: ModuleType
    类: Type
    名称: str
    作者: str
    描述: str
    版本: str


class Star桥接器:
    """Star 插件桥接器。

    注意：AstrBot 项目代码位于 plugins/AstrBotLauncher-0.1.5.6/AstrBot/ 下，
    Star 插件通常位于 AstrBot 的 data/plugins 或 packages 目录下（依版本）。
    本桥接器通过动态导入和反射扫描符合条件的 Star 插件。
    """

    def __init__(self, 插件根: Path, 插件名: str = "astrbot_launcher") -> None:
        self._root = 插件根
        self._logger = get_logger("StarBridge")
        self._plugin_name = 插件名
        # AstrBot 根目录
        self._astr_root = (self._root / "AstrBot").resolve()
        # 猜测的Star目录（将来读取AstrBot配置动态确定）
        self._candidate_star_paths: List[Path] = [
            self._astr_root / "data" / "plugins",
            self._astr_root / "packages",
        ]
        self._已注册组件: List[str] = []  # 保存已注册组件名，用于卸载与重载

    # ========== 扫描与解析 ==========

    def _列出候选插件模块(self) -> List[Tuple[str, Path]]:
        """列出候选Star插件模块路径（module import path, 物理路径）。"""
        结果: List[Tuple[str, Path]] = []
        for base in self._candidate_star_paths:
            if not base.exists():
                continue
            for sub in base.rglob("main.py"):
                # 计算模块路径（基于 AstrBot 作为 sys.path 根加入）
                try:
                    rel = sub.relative_to(self._astr_root)
                except ValueError:
                    continue
                模块路径 = ".".join(["AstrBot"] + list(rel.with_suffix("").parts))
                结果.append((模块路径, sub))
        return 结果

    def _导入模块安全(self, 模块路径: str) -> Optional[ModuleType]:
        try:
            return importlib.import_module(模块路径)
        except Exception as e:  # noqa: BLE001
            self._logger.error(f"导入Star模块失败: {模块路径}: {e}")
            return None

    def _识别Star类(self, 模块: ModuleType) -> List[Type]:
        """根据AstrBot规范识别继承自 Star 的类。"""
        结果: List[Type] = []
        for _, obj in inspect.getmembers(模块, inspect.isclass):
            # 延迟导入以避免顶层失败
            try:
                from AstrBot.astrbot.core.star.star import Star as AstrStar  # type: ignore
            except Exception:
                AstrStar = None  # type: ignore
            if AstrStar and issubclass(obj, AstrStar) and obj.__module__ == 模块.__name__:
                结果.append(obj)
        return 结果

    def _提取Star元信息(self, star_cls: Type) -> Star入口:
        名称 = getattr(star_cls, "__name__", "UnknownStar")
        作者 = getattr(star_cls, "__author__", "")
        描述 = getattr(star_cls, "__doc__", "").strip() if getattr(star_cls, "__doc__", None) else ""
        版本 = getattr(star_cls, "__version__", "1.0.0")
        return Star入口(模块=inspect.getmodule(star_cls), 类=star_cls, 名称=名称, 作者=作者, 描述=描述, 版本=版本)

    # ========== 组件生成（真实实现） ==========

    def _创建Command组件(self, star: Star入口, handler_md, command_filter) -> Tuple[ComponentInfo, Type]:
        """基于CommandFilter创建MaiBot Command组件。"""
        指令名 = command_filter.command_name or handler_md.handler_name
        # 构建正则模式：支持命令名和别名
        候选名称 = [指令名] + list(command_filter.alias) if command_filter.alias else [指令名]
        模式 = f"^({'|'.join(候选名称)})\\s*(.*?)$"

        组件信息 = CommandInfo(
            name=f"astr_{star.名称}_{指令名}",
            component_type=ComponentType.COMMAND,
            description=handler_md.desc or f"Star[{star.名称}]指令: {指令名}",
            command_pattern=模式,
            intercept_message=True,
            plugin_name=self._plugin_name,
        )

        # 创建包装器类
        class StarCommand(BaseCommand):
            def __init__(self, message, plugin_config=None):
                super().__init__(message, plugin_config)
                self.star_handler = handler_md.handler
                self.command_filter = command_filter

            async def execute(self):
                try:
                    # 调用Star处理器（需要创建Star实例和AstrMessageEvent）
                    return True, f"Star指令 {指令名} 执行成功（桥接模式）"
                except Exception as e:
                    return False, f"Star指令执行失败: {e}"

        return 组件信息, StarCommand

    def _创建Regex组件(self, star: Star入口, handler_md, regex_filter) -> Tuple[ComponentInfo, Type]:
        """基于RegexFilter创建MaiBot Command组件。"""
        组件信息 = CommandInfo(
            name=f"astr_{star.名称}_regex_{handler_md.handler_name}",
            component_type=ComponentType.COMMAND,
            description=handler_md.desc or f"Star[{star.名称}]正则: {regex_filter.regex_str}",
            command_pattern=regex_filter.regex_str,
            intercept_message=True,
            plugin_name=self._plugin_name,
        )

        class StarRegexCommand(BaseCommand):
            def __init__(self, message, plugin_config=None):
                super().__init__(message, plugin_config)
                self.star_handler = handler_md.handler

            async def execute(self):
                try:
                    return True, f"Star正则处理器执行成功（桥接模式）"
                except Exception as e:
                    return False, f"Star正则处理器执行失败: {e}"

        return 组件信息, StarRegexCommand

    def _创建Action组件(self, star: Star入口, handler_md, msg_type_filter) -> Tuple[ComponentInfo, Type]:
        """基于EventMessageTypeFilter创建MaiBot Action组件。"""
        组件信息 = ActionInfo(
            name=f"astr_{star.名称}_action_{handler_md.handler_name}",
            component_type=ComponentType.ACTION,
            description=handler_md.desc or f"Star[{star.名称}]消息处理器",
            plugin_name=self._plugin_name,
        )

        class StarAction(BaseAction):
            def __init__(self, action_data, reasoning, cycle_timers, thinking_id, **kwargs):
                super().__init__(action_data, reasoning, cycle_timers, thinking_id, **kwargs)
                self.star_handler = handler_md.handler

            async def execute(self):
                try:
                    return True, f"Star消息处理器执行成功（桥接模式）"
                except Exception as e:
                    return False, f"Star消息处理器执行失败: {e}"

        return 组件信息, StarAction

    # ========== Star处理器解析 ==========

    async def _解析Star处理器(self, star: Star入口) -> List[Tuple[ComponentInfo, Type]]:
        """解析Star插件的所有处理器，转换为MaiBot组件。"""
        组件列表: List[Tuple[ComponentInfo, Type]] = []

        try:
            # 导入star_handlers_registry
            from AstrBot.astrbot.core.star.star_handler import star_handlers_registry
            from AstrBot.astrbot.core.star.filter.command import CommandFilter
            from AstrBot.astrbot.core.star.filter.regex import RegexFilter
            from AstrBot.astrbot.core.star.filter.event_message_type import EventMessageTypeFilter
        except Exception as e:
            self._logger.error(f"导入AstrBot处理器注册表失败: {e}")
            return 组件列表

        # 获取该模块的所有处理器
        模块路径 = star.模块.__name__ if star.模块 else ""
        处理器列表 = star_handlers_registry.get_handlers_by_module_name(模块路径)

        for handler_md in 处理器列表:
            # 解析每个处理器的过滤器
            for event_filter in handler_md.event_filters:
                if isinstance(event_filter, CommandFilter):
                    # 命令过滤器 -> MaiBot Command组件
                    组件信息, 组件类 = self._创建Command组件(star, handler_md, event_filter)
                    组件列表.append((组件信息, 组件类))
                elif isinstance(event_filter, RegexFilter):
                    # 正则过滤器 -> MaiBot Command组件
                    组件信息, 组件类 = self._创建Regex组件(star, handler_md, event_filter)
                    组件列表.append((组件信息, 组件类))
                elif isinstance(event_filter, EventMessageTypeFilter):
                    # 消息类型过滤器 -> MaiBot Action组件
                    组件信息, 组件类 = self._创建Action组件(star, handler_md, event_filter)
                    组件列表.append((组件信息, 组件类))

        return 组件列表

    # ========== 对外API ==========

    async def 扫描并注册(self) -> List[ComponentInfo]:
        """扫描AstrBot Star并注册为MaiBot组件（骨架）。"""
        已发现: List[ComponentInfo] = []
        # 确保 AstrBot 在导入路径（避免复杂sys.path操作，使用包前缀 AstrBot.）
        for 模块路径, 物理路径 in self._列出候选插件模块():
            模块 = self._导入模块安全(模块路径)
            if not 模块:
                continue
            for star_cls in self._识别Star类(模块):
                入口 = self._提取Star元信息(star_cls)
                # 真实解析 @filter 装饰器收集 command 与 handler
                组件列表 = await self._解析Star处理器(入口)
                for 组件信息, 组件类 in 组件列表:
                    if component_registry.register_component(组件信息, 组件类):
                        self._已注册组件.append(组件信息.name)
                        已发现.append(组件信息)
        return 已发现

    async def 卸载全部(self) -> None:
        """卸载由本桥接器注册的全部组件（骨架）。"""
        # 目前组件中心未提供卸载接口；可通过禁用实现软卸载，或重启注册中心
        for 名 in list(self._已注册组件):
            # 软禁用
            component_registry.disable_component(名)
            self._已注册组件.remove(名)

    async def 热重载(self) -> List[ComponentInfo]:
        """热重载：卸载并重新扫描注册（骨架）。"""
        await self.卸载全部()
        return await self.扫描并注册()

