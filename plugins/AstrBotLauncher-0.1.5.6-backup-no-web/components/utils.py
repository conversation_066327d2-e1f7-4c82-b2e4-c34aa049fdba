# -*- coding: utf-8 -*-
"""
通用工具模块（企业级实现）

功能：
- 日志初始化与格式化
- 配置加载与校验
- 异常定义与统一处理
- 时间与重试、缓存等通用函数

说明：所有中文注释与文档字符串均使用UTF-8编码，无BOM。
"""
from __future__ import annotations

import asyncio
import json
import logging
import os
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Dict, Optional


# ========== 日志相关 ==========

_DEF_LOG_FORMAT = "%(asctime)s | %(levelname)s | %(name)s | %(message)s"


def 初始化日志(名称: str = "AstrBotLauncher", 级别: int = logging.INFO, 日志文件: Optional[str] = None) -> logging.Logger:
    """初始化并返回带有控制台与可选文件输出的日志记录器。

    参数：
    - 名称：日志记录器名称
    - 级别：日志级别，默认INFO
    - 日志文件：可选，将日志写入该文件（路径会自动创建）
    """
    logger = logging.getLogger(名称)
    logger.setLevel(级别)

    # 防止重复添加handler
    if not logger.handlers:
        ch = logging.StreamHandler(sys.stdout)
        ch.setLevel(级别)
        ch.setFormatter(logging.Formatter(_DEF_LOG_FORMAT))
        logger.addHandler(ch)

        if 日志文件:
            Path(日志文件).parent.mkdir(parents=True, exist_ok=True)
            fh = logging.FileHandler(日志文件, encoding="utf-8")
            fh.setLevel(级别)
            fh.setFormatter(logging.Formatter(_DEF_LOG_FORMAT))
            logger.addHandler(fh)

    return logger


# ========== 配置相关 ==========

class 配置错误(Exception):
    """配置相关错误。"""


@dataclass
class 配置加载结果:
    路径: Path
    数据: Dict[str, Any]


def 加载并验证配置(路径或文件: str | os.PathLike, 必填键: Optional[Dict[str, type]] = None) -> 配置加载结果:
    """加载JSON配置并按需验证必填键与类型。

    参数：
    - 路径或文件：配置文件路径
    - 必填键：形如 {"ipc_settings.port": int} 的键路径与类型字典
    """
    cfg_path = Path(路径或文件)
    if not cfg_path.exists():
        raise 配置错误(f"配置文件不存在：{cfg_path}")

    try:
        data = json.loads(cfg_path.read_text(encoding="utf-8"))
    except json.JSONDecodeError as e:
        raise 配置错误(f"配置JSON解析失败：{e}") from e

    def _get_by_path(d: Dict[str, Any], key_path: str) -> Any:
        cur: Any = d
        for seg in key_path.split("."):
            if not isinstance(cur, dict) or seg not in cur:
                return None
            cur = cur[seg]
        return cur

    if 必填键:
        for k, t in 必填键.items():
            val = _get_by_path(data, k)
            if val is None:
                raise 配置错误(f"缺少必填配置项：{k}")
            if not isinstance(val, t):
                raise 配置错误(f"配置项类型错误：{k} 需要 {t.__name__} 实际 {type(val).__name__}")

    return 配置加载结果(路径=cfg_path, 数据=data)


# ========== 异步工具 ==========

async def 安全运行(协程函数: Callable[..., Any], *args, 异常处理: Optional[Callable[[BaseException], Any]] = None, **kwargs) -> Any:
    """安全执行协程，捕获异常并委派给异常处理回调。"""
    try:
        return await 协程函数(*args, **kwargs)
    except BaseException as e:  # noqa: BLE001
        if 异常处理:
            return 异常处理(e)
        raise


def 异步重试(次数: int = 3, 间隔秒: float = 1.0) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """简单的异步重试装饰器（线性退避）。"""

    def 装饰器(fn: Callable[..., Any]) -> Callable[..., Any]:
        async def 包装(*args, **kwargs):
            last_exc: Optional[BaseException] = None
            for i in range(max(1, 次数)):
                try:
                    return await fn(*args, **kwargs)
                except BaseException as e:  # noqa: BLE001
                    last_exc = e
                    await asyncio.sleep(间隔秒)
            assert last_exc is not None
            raise last_exc

        return 包装

    return 装饰器

