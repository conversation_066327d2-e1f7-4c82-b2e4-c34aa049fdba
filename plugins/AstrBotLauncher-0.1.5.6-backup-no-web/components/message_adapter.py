# -*- coding: utf-8 -*-
"""
消息适配器（骨架）
- AstrBot AstrMessageEvent ↔ MaiBot MessageRecv 双向转换
"""
from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict

from src.chat.message_receive.message import MessageRecv


@dataclass
class Astr事件视图:
    平台: str
    会话ID: str
    发送者ID: str
    纯文本: str
    原始: Dict[str, Any]


class 消息适配器:
    """消息格式转换器。"""

    async def astr_to_maim(self, astr_event: Astr事件视图) -> MessageRecv:
        """Astr → MaiBot 转换（简化骨架）。"""
        # TODO: 根据AstrBot文档补充更多字段映射
        return MessageRecv(
            sender_id=astr_event.发送者ID,
            session_id=astr_event.会话ID,
            platform=astr_event.平台,
            text=astr_event.纯文本 or "",
            raw_message=astr_event.原始,
        )

    async def maim_to_astr(self, message: MessageRecv) -> Astr事件视图:
        """MaiBot → Astr 转换（简化骨架）。"""
        return Astr事件视图(
            平台=message.platform,
            会话ID=message.session_id,
            发送者ID=message.sender_id,
            纯文本=message.text,
            原始=message.raw_message or {},
        )

