# -*- coding: utf-8 -*-
"""
进程管理器（AstrBot子进程）
- 启动、监控、优雅停止、自动重启
- 健康检查与日志采集（骨架）
"""
from __future__ import annotations

import asyncio
import os
import signal
import sys
from asyncio.subprocess import Process
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

from .utils import 初始化日志, 加载并验证配置, 配置错误


@dataclass
class 进程配置:
    python可执行: str
    工作目录: Path
    主脚本: Path
    环境变量: dict
    启动超时秒: int = 60
    关闭超时秒: int = 30
    自动重启: bool = True
    最大重启次数: int = 3
    重启间隔秒: int = 5


class AstrBot进程管理器:
    """AstrBot 子进程生命周期管理器。"""

    def __init__(self, 插件根目录: Path) -> None:
        self._root = 插件根目录
        self._logger = 初始化日志("AstrBotProcess")
        self._proc: Optional[Process] = None
        self._restart_times = 0
        # 读取配置
        cfg = 加载并验证配置(
            self._root / "config.json",
            {
                "astrbot_settings.python_executable": str,
                "astrbot_settings.working_directory": str,
                "astrbot_settings.executable_path": str,
            },
        ).数据
        self._cfg = 进程配置(
            python可执行=cfg["astrbot_settings"]["python_executable"],
            工作目录=(self._root / cfg["astrbot_settings"]["working_directory"]).resolve(),
            主脚本=(self._root / cfg["astrbot_settings"]["executable_path"]).resolve(),
            环境变量=cfg["astrbot_settings"].get("environment_variables", {}),
            启动超时秒=cfg["astrbot_settings"].get("startup_timeout", 60),
            关闭超时秒=cfg["astrbot_settings"].get("shutdown_timeout", 30),
            自动重启=cfg["astrbot_settings"].get("auto_restart", True),
            最大重启次数=cfg["astrbot_settings"].get("max_restart_attempts", 3),
            重启间隔秒=cfg["astrbot_settings"].get("restart_delay", 5),
        )

    async def 启动(self) -> None:
        """启动AstrBot子进程。"""
        if self._proc and self._proc.returncode is None:
            self._logger.info("AstrBot 子进程已在运行")
            return

        self._logger.info("正在启动 AstrBot 子进程…")
        env = os.environ.copy()
        env.update({k: str(v) for k, v in self._cfg.环境变量.items()})
        self._cfg.工作目录.mkdir(parents=True, exist_ok=True)

        self._proc = await asyncio.create_subprocess_exec(
            self._cfg.python可执行,
            str(self._cfg.主脚本),
            cwd=str(self._cfg.工作目录),
            env=env,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )

        try:
            await asyncio.wait_for(self._健康检查(初始=True), timeout=self._cfg.启动超时秒)
        except asyncio.TimeoutError:
            self._logger.error("启动超时，准备终止子进程…")
            await self.终止(强制=True)
            raise

        self._logger.info("AstrBot 子进程启动完成")
        asyncio.create_task(self._读取日志循环())

    async def _读取日志循环(self) -> None:
        assert self._proc is not None
        assert self._proc.stdout and self._proc.stderr
        while True:
            if self._proc.returncode is not None:
                self._logger.warning(f"子进程已退出，返回码={self._proc.returncode}")
                if self._cfg.自动重启 and self._restart_times < self._cfg.最大重启次数:
                    self._restart_times += 1
                    self._logger.info(f"{self._cfg.重启间隔秒}s 后尝试重启，第{self._restart_times}次…")
                    await asyncio.sleep(self._cfg.重启间隔秒)
                    await self.启动()
                break
            line = await self._proc.stdout.readline()
            if not line:
                await asyncio.sleep(0.1)
                continue
            self._logger.info(f"[AstrBot] {line.decode(errors='ignore').rstrip()}")

    async def _健康检查(self, 初始: bool = False) -> None:
        # 预留：调用IPC心跳或HTTP健康接口
        await asyncio.sleep(0.3 if 初始 else 0.0)

    async def 停止(self) -> None:
        """优雅停止。"""
        if not self._proc or self._proc.returncode is not None:
            return
        self._logger.info("正在请求优雅停止 AstrBot…")
        try:
            self._proc.send_signal(signal.SIGINT if os.name != "nt" else signal.CTRL_BREAK_EVENT)
        except Exception:  # noqa: BLE001
            pass
        try:
            await asyncio.wait_for(self._proc.wait(), timeout=self._cfg.关闭超时秒)
            self._logger.info("AstrBot 已优雅停止")
        except asyncio.TimeoutError:
            self._logger.warning("优雅停止超时，转为强制终止…")
            await self.终止(强制=True)

    async def 终止(self, 强制: bool = False) -> None:
        if not self._proc or self._proc.returncode is not None:
            return
        if 强制:
            self._logger.error("强制结束 AstrBot 进程…")
            self._proc.kill()
        else:
            self._proc.terminate()
        try:
            await asyncio.wait_for(self._proc.wait(), timeout=10)
        except asyncio.TimeoutError:
            self._proc.kill()
            await self._proc.wait()
        self._logger.info("AstrBot 子进程已终止")

