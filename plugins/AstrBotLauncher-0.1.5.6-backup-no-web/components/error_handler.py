# -*- coding: utf-8 -*-
"""
错误处理器（企业级骨架）
- 错误分类（配置错误、连接错误、协议错误、超时、资源不足）
- 自动恢复策略（重试、重连、重启子进程、降级）
- 与日志系统集成
"""
from __future__ import annotations

import asyncio
from dataclasses import dataclass
from enum import Enum
from typing import Callable, Optional

from src.common.logger import get_logger


class 错误类型(Enum):
    配置错误 = "CONFIG"
    连接错误 = "CONNECTION"
    协议错误 = "PROTOCOL"
    超时错误 = "TIMEOUT"
    资源不足 = "RESOURCE"
    未知错误 = "UNKNOWN"


@dataclass
class 错误上下文:
    类型: 错误类型
    信息: str
    可恢复: bool = True
    重试次数: int = 0


class 错误处理器:
    """集中式错误处理与恢复。"""

    def __init__(self) -> None:
        self._logger = get_logger("ErrorHandler")

    def 分类(self, e: BaseException) -> 错误上下文:
        名 = e.__class__.__name__.lower()
        if "timeout" in 名:
            return 错误上下文(类型=错误类型.超时错误, 信息=str(e))
        if "config" in 名:
            return 错误上下文(类型=错误类型.配置错误, 信息=str(e), 可恢复=False)
        if "connection" in 名 or "socket" in 名:
            return 错误上下文(类型=错误类型.连接错误, 信息=str(e))
        return 错误上下文(类型=错误类型.未知错误, 信息=str(e))

    async def 处理(self, 上下文: 错误上下文, 恢复回调: Optional[Callable[[], None]] = None) -> None:
        self._logger.error(f"错误[{上下文.类型.value}] {上下文.信息}")
        if not 上下文.可恢复:
            return
        # 简单恢复策略：指数退避等待后调用回调
        等待 = min(2 ** max(0, 上下文.重试次数), 30)
        await asyncio.sleep(等待)
        if 恢复回调:
            try:
                恢复回调()
            except Exception as e:  # noqa: BLE001
                self._logger.error(f"恢复回调执行失败: {e}")

