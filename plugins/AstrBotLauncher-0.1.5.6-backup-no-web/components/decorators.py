# -*- coding: utf-8 -*-
"""
装饰器模块
- 重试、缓存、性能监控（骨架实现）
"""
from __future__ import annotations

import asyncio
import functools
import time
from typing import Any, Callable, Dict, Tuple


def 指数退避重试(最大次数: int = 3, 初始间隔: float = 0.5, 倍率: float = 2.0):
    """异步函数的指数退避重试装饰器。"""

    def 装饰器(fn: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(fn)
        async def 包装(*args, **kwargs):
            等待 = 初始间隔
            异常: Exception | None = None
            for _ in range(max(1, 最大次数)):
                try:
                    return await fn(*args, **kwargs)
                except Exception as e:  # noqa: BLE001
                    异常 = e
                    await asyncio.sleep(等待)
                    等待 *= 倍率
            assert 异常 is not None
            raise 异常

        return 包装

    return 装饰器


def 简单LRU缓存(maxsize: int = 128):
    """简单LRU缓存（线程不安全，适用于轻量异步场景）。"""
    缓存: Dict[Tuple, Tuple[float, Any]] = {}

    def 装饰器(fn: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(fn)
        async def 包装(*args, **kwargs):
            key = (args, tuple(sorted(kwargs.items())))
            if key in 缓存:
                缓存[key] = (time.time(), 缓存[key][1])
                return 缓存[key][1]
            结果 = await fn(*args, **kwargs)
            缓存[key] = (time.time(), 结果)
            if len(缓存) > maxsize:
                # 移除最老的
                最老键 = min(缓存.items(), key=lambda x: x[1][0])[0]
                缓存.pop(最老键, None)
            return 结果

        return 包装

    return 装饰器


def 性能计时(名称: str = "操作"):
    """简单性能计时装饰器。"""

    def 装饰器(fn: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(fn)
        async def 包装(*args, **kwargs):
            开始 = time.perf_counter()
            结果 = await fn(*args, **kwargs)
            结束 = time.perf_counter()
            print(f"[性能] {名称} 用时 {(结束-开始)*1000:.2f} ms")
            return 结果

        return 包装

    return 装饰器

