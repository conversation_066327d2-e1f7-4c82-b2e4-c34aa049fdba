# What's Changed

> 📢 在升级前，请完整阅读本次更新日志。

## ✨ 新增的功能

1. 安装完插件后自动弹出插件仓库 README 对话框 @zhx8702
4. 支持阿里云百炼 TTS@Soulter
5. 支持 Telegram MarkdownV2 渲染  @Soulter
6. 支持 钉钉 Markdown 渲染 @Soulter
6. 增加对 Gemini 系列模型的输入安全设置参数支持 @AliveGh0st
7. 支持手动设置时区以应对容器、国外用户的时区问题 @anka-afk @Raven95676 @Soulter
8. 插件市场显示帮助按钮 @Soulter

## 🎈 功能性优化

1. WebUI 的日志通信使用 SSE 替代 Websockets @Soulter
2. 在发送消息之前统一检查消息内容是否为空, 不允许发送空消息, 以解决该消息内容不支持查看以及 Gemini 返回 `<empty content>` 问题 @anka-afk
3. 更新 Dify 平台链接为官方域名 by @Captain-Slacker-OwO
4. 人格 prompt 输入框支持调节高度 @Soulter

## 🐛 修复的 Bug

1. 将最多携带对话数量修改回 `-1` 时出现报错 #1074 @anka-afk
2. 修复无法识别到函数调用异常的问题 by @Soulter
3. 修复 aiocqhttp 适配器下空白 plain 导致的 `the object is not a proper segment chain` 报错问题 @Soulter
4. 修复阿里百炼应用无法多轮会话的问题 @Soulter

## 🧩 新增的插件

待补充
