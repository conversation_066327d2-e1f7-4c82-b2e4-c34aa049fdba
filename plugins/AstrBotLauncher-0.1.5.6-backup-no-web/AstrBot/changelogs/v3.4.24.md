# What's Changed

0. ✨ 新增: 支持正则表达式匹配触发机器人，机器人在某一段时间内持续唤醒（不用输唤醒词）。（安装 astrbot_plugin_wake_enhance 插件）
2. ✨ 新增: 可以通过 /tts 开关TTS，通过 /provider 更换 TTS #436
3. ✨ 新增: 管理面板支持设置 GitHub 反向代理地址以优化中国大陆地区下载 AstrBot 插件的速度。（在管理面板-设置页）
4. 🐛 修复: 修复指令不经过唤醒前缀也能生效的问题。在引用消息的时候无法使用前缀唤醒机器人 #444
5. 🐛 修复: 修复 Napcat 下戳一戳消息报错
6. 👌 优化: 从压缩包上传插件时，去除仓库 -branch 尾缀
7. 🐛 修复: gemini 报错时显示 apikey
8. 🐛 修复: drun 不支持函数调用的报错
9. 🐛 修复: raw_completion 没有正确传递导致部分插件无法正常运作 #439