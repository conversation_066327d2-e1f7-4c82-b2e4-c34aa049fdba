# What's Changed

1. 修复了 reminder 插件可能不会触发回调的问题。
2. 修复了 telegram 插件不可用的问题。
3. 修复了 qq_official 无法发图的问题。
4. 修复事件监听器会让 WakeStage 失效的问题。
5. 修复 websearch 在 cmd_config 中失效的问题。
3. 支持通过 Google GenAI 访问 Gemini 模型，而不需要使用 Gemini 对 OpenAI 的兼容 API。详见文档。
4. 支持对插件禁用/启用。/plugin off/on <plugin_name>
5. 支持基于 Docker 的沙箱化代码执行器。（Beta 测试）详见文档。
6. 支持接入 Dify LLMOps 平台。详见文档。
7. 适配器类插件支持设置默认配置模板。
8. 优化了部分指令的持久化记忆。如 /tool 的禁用、/provider 的选择都将持久化保存，每次启动时不需要重新设置。
9. 优化了 glm-4v-flash 模型。其只支持一张图。