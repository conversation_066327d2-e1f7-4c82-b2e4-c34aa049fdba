# What's Changed

> 📢 在升级前，请完整阅读本次更新日志。

## ✨ 新增的功能

1. 适配 `gemini-2.0-flash-exp-image-generation` 对图片模态的输入 [#1017](https://github.com/Soulter/AstrBot/issues/1017)
2. 在 MessageChain 类中添加 at 和 at_all 方法，用于快速添加 At 消息 @left666
3. Gewechat Client 增加获取通讯录列表接口
4. 支持 /llm 指令快捷启停 LLM 功能 [#296](https://github.com/Soulter/AstrBot/issues/296)

## 🎈 功能性优化

1. Edge TTS 支持使用代理
2. 在 Lifecycle 新增插件资源清理逻辑 @Raven95676
3. Docker 镜像提供内置 FFmpeg [#979](https://github.com/Soulter/AstrBot/issues/979)
4. 优化无对话情况下设置人格的反馈 @Raven95676
5. 若禁用提供商，自动切换到另一个可用的提供商 @Raven95676
6. openai_source 同步支持随机请求均衡，同时优化 LLM 请求逻辑的异常处理
7. 保存 shared_preferences 时强制刷新文件缓冲区
8. 优化空 At 回复 @advent259141

## 🐛 修复的 Bug

1. 插件更新时没有正确应用加速地址
2. newgroup 指令名显示错误

## 🧩 新增的插件

待补充
