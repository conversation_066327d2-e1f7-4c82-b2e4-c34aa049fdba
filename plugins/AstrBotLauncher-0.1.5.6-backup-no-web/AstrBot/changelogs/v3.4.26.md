# What's Changed

1. ✨ 新增: 支持 Webhook 方式接入 QQ 官方机器人接口
2. ✨ 新增: 支持完善的 Dify Chat 模式对话管理，包括 /new /switch /del /ls /reset 均已适配 Dify Chat 模式。
3. ✨ 新增: 支持基于对数函数的分段回复延时时间计算 #414
4. ✨ 新增: 支持设置管理面板的端口号
5. ✨ 新增: 支持对大模型的响应进行内容审查 #474
6. 🐛 修复: gewechat 不能发送主动消息 #402
7. 🐛 修复: dify Chat 模式无法重置会话 #469
8. 🐛 修复: ensure result is retrieved again to handle potential plugin chain replacements
9. 🐛 优化: 将 Gewechat 所有事件下发到流水线供插件开发
10. 🐛 修复: correct dashboard update tooltip typo by @Akuma-real
