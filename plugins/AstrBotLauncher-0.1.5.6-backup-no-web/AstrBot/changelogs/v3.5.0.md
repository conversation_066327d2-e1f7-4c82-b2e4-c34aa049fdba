# What's Changed

> 📢 AstrBot 上架宝塔面板 Docker 应用商店了！
> 📢 在升级前，请完整阅读本次更新日志。

## ✨ 新增的功能

1. ‼️ 新增支持接入 MCP 服务器 @Soulter @AraragiEro
1. ‼️ 新增支持本地渲染 Markdown，并支持自定义字体，详见 -> [#957](https://github.com/Soulter/AstrBot/issues/957#issuecomment-2749981802)
2. 新增支持在 WebUI 管理所有与大模型的对话
3. 适配完整的 function-calling 流程。[#804](https://github.com/Soulter/AstrBot/issues/804) [#566](https://github.com/Soulter/AstrBot/issues/566)
4. 新增支持消息平台热重载，不再需要重启 AstrBot
5. 新增支持阿里云百炼应用的 RAG 应用 [#878](https://github.com/Soulter/AstrBot/issues/878)
6. 新增 `/plugin get` OP 指令下载插件。如 `/plugin get Raven95676/astrbot_plugin_wordle`
7. 新增 `/newgroup` OP 指令，支持私聊 bot 给指定群聊创建新的对话。by @LunarMeal
8. Gewechat 下支持 `添加好友`, `接收/发送视频`, `获取群信息`, `接收/发送表情包` by @Moyuyanli @Soulter @XuYingJie-cmd @NiceAir
9. Telegram 下支持接收和处理表情包(Sticker) @Raven95676


## 🎈 功能性优化

0. 更加美观的 WebUI 设计，降低疲劳程度。
1. 微信下，忽略 `微信团队` 的消息 [#859](https://github.com/Soulter/AstrBot/issues/859)
2. 完善 Dify 的图片输入功能 [#893](https://github.com/Soulter/AstrBot/issues/893)
3. 消息平台和配置提供商配置页中，自动更新旧的配置项
4. 优化钉钉在配置错误之后堵塞整个线程的问题 [#885](https://github.com/Soulter/AstrBot/issues/885)
5. WebUI 删除插件时提供二次确认避免误删 @zhx8702
6. WebUI 优化新版本时的信息显示
7. 发送消息失败时的报错回显优化
8. 改善所有消息平台的优雅退出逻辑
9. 空 @ 时调用 LLM 获得更加富有人格的回复 by @advent259141

## 🐛 修复的 Bug

1. 修复图片没有被存储到聊天上下文历史记录
2. 修复 Telegram 下无法识别图片描述(Caption) [#910](https://github.com/Soulter/AstrBot/issues/910)
3. 修复 Telegram Topic 群组下引用消息来源错误的问题 [#908](https://github.com/Soulter/AstrBot/issues/908)
4. 修复 Telegram 下 `/start` 指令的一些问题 [#751](https://github.com/Soulter/AstrBot/issues/751)
5. WebUI 插件市场卡片显示风格的过滤问题。[#927](https://github.com/Soulter/AstrBot/issues/927)
6. 统一 SSL 证书验证逻辑，修复 `SSLCertVerificationError` 的问题。by @IGCrystal [#950](https://github.com/Soulter/AstrBot/issues/950)
7. 修复可能形成 SQL 注入的风险
8. 修复本地上传插件时无法重载插件的问题 [#995](https://github.com/Soulter/AstrBot/issues/995) by @zhx8702

## 🧩 新增的插件

1. astrbot_plugin_majsoul-master - 雀魂多功能插件 - by @kterna
2. astrbot_plugin_server - 可视化服务器状态卡片，/status 或 /状态查询 查看 - by @yanfd @Meguminlove
3. astrbot_plugin_Getcwm - 刺猬猫小说数据获取与画图插件 - by @Li-shi-ling
4. astrbot_plugin_anti_withdrawal - 防撤回插件，目前只支持微信私聊群聊的文本消息，将撤回的消息记录并发送给设定的人 - by @NiceAir
5. astrbot_plugin_hello77 -  游戏梗自动回复插件 - by @ttq7
6. astrbot_plugin_push_lite - Webhook 轻量级推送插件 - @Raven95676
7. astrbot_plugin_pokecheck - 检测“戳”关键词的插件 - @huanyan434
8. astrbot_plugin_MultiAI_PollPad - 轮询调用配置的大语言模型输出多个结果。同时将 AI 结果拷贝至在线文本编辑器 - by @Ynkcc
9. astrbot_plugin_box - / - by @Zhalslar
10. astrbot_plugin_Translation - 通过调用百度翻译 API 实现翻译文本 - by @zengweis
11. astrbot_plugin_wordle_2 - Wordle 游戏插件 - by @Raven95676 @whzcc
12. astrbot_plugin_mai_sgin - 舞萌出勤与退勤签到插件 - by @Rinyin
13. astrbot_plugin_Lolicon - Lolicon API 随机动漫图片插件 - by @ttq7
14. astrbot_plugin_aiocensor - 综合内容安全+群管插件 - by @Raven95676