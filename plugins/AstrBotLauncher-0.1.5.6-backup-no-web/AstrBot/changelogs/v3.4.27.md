# What's Changed

1. ✨ 新增: 支持日语版本的 Readme by @eltociear
2. ✨ 新增: 主动回复支持白名单 #488
3. ⚡ 优化: 面板数据展示图表的时区问题 #460
4. ⚡ 优化: 针对 id 对模型号进行排序以适配 OneAPI 乱序情况 #384
5. ✨ 新增: 支持对大模型的响应进行内容审查 #474
6. 🐛 修复: 修复保存插件配置时没有检查类型合法性的问题
7. 🐛 修复: 尝试修复 Gemini empty text 相关报错
8. 🐛 修复: dify 不能正常使用 set/unset 指令定义动态变量 #482
9. 🐛 修复: 不能在 Webhook 模式下的 QQ 官方 API 私聊 #484
10. 🐛 修复: 在没有触发并且没通过安全审查的情况下仍然发送了未通过消息
11. 🐛 修复: /del 指令导致的相关异常
12. 🐛 修复: 在 Gewechat 中不能先写内容后 @ 机器人 #492