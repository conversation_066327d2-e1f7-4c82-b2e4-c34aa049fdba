# What's Changed

> Special thanks for all contributors and plugin developers and users who love AstrBot. 💖

## ✨ 新增的功能

1. 支持解析回复消息，支持 LLM 对所引用消息具有感知 #783
2. 支持 Dify 的文件、图片、视频、音频输出 #819
3. QQ 下支持嵌套转发(napcat) @zouyonghe
4. 配置页样式重写，更紧凑的 WebUI 配置

## 🎈 功能性优化

1. 使用系统时间而不是 UTC+8 时间作为默认时间以适应海外用户需求 @roeseth
2. 在对话隔离情况下也可以将整个群聊加入白名单 #746
3. 在调用插件异常时更完整的报错输出
4. gewechat 下对已知且没有业务处理的事件类型不显示详细日志 @diudiu62
5. 优化 WebUI 悬浮文档 @IGCrystal
6. 支持自定义 WebUI、Wecom Webhook Server, QQ Official Webhook Server 的 host #821
7. Dify 下当只有图片输入时的默认 prompt 防止一些报错 #837

## 🐛 修复的 Bug

1.  fishaudio 默认 baseurl 不可用
2.  gewechat 下重复登录后提示设备不存在导致无法重新登陆 @beat4ocean
3.  gewechat 下用户本人发消息会触发消息回复 @beat4ocean
4.  钉钉 WebUI 文档不显示
5.  更新插件后插件热重载不完全、函数工具重复添加
6.  OpenAI TTS API TypeError 报错 #755
7.  EdgeTTS 部分情况下无法使用 @Soulter @需要哦
8.  QQ 官方机器人平台下发送 base64 图片消息段报错 @Soulter @shuiping233
9.  QQ 官方机器人平台下命令参数报错信息无法正常发送 @shuiping233
10. WebUI 错误地显示未知更新
11. 部分情况下文件无法上传到 Telegram 群组 #601
12. 插件管理的插件简介太长导致 “帮助”“操作”图标不显示 #790
13. LLOnebot 合并消息转发错误 #842
14. model_config 中自定义的配置项（如温度）类型自动变回 string #854

## 🧩 新增的插件

1. astrbot_plugin_image_understanding_Janus-Pro - 使用deepseek-ai/Janus-Pro系列模型为本地模型提供的图片理解补充 @xiewoc
2. astrbot_plugin_moyurenpro - 摸鱼人日历，支持自定义时间时区，自定义api，支持立即发送，工作日定时发送。 @quirrel-zh @DuBwTf
3. astrbot_plugin_wechat_manager - 微信关键字好友自动审核、关键字邀请进群。@diudiu62
4. astrbot_plugin_qwq_filter - qwq 思考过滤工具 @beat4ocean
5. astrbot_plugin_chatsummary - 一个通过拉取历史聊天记录，调用LLM大模型接口实现消息总结功能。@laopanmemz
6. astrBot_PGR_Dialogue - 检测到部分战双角色的名称(或别称)时，有概率发送一条语音文本 @KurisuRee7
7. astrbot_plugin_bv -  解析群内https://www.bilibili.com/video/BV号/ 的链接并获取视频数据与视频文件，以合并转发方式发送 @haliludaxuanfeng
8. astrbot_plugin_gemini_exp - 让你在AstrBot调用Gemini2.0-flash-exp来生成图片或者p图。Gemini2.0-flash-exp为原生多模态模型，其既是语言模型，也是生图模型，因此能够对图像使用简单的自然语言命令进行处理。@Elen123bot
9. astrbot_plugin_sjzb - 随机生成绝地潜兵2游戏中一组4个战备配置 @tenno1174
10. astrbot_plugin_picture_manager - 图片管理插件，允许用户通过自定义触发指令从API或直接URL获取图片。@bigshabei
11. astrbot_plugin_bilibiliParse - 解析哔哩哔哩视频，并以图片的形式发送给用户 @7Hello12
12. astrbot_plugin_sensoji - 这是一个模拟日本浅草寺抽签功能的插件。用户可以通过发送 /抽签 命令随机抽取一个签文，获取运势提示。签文包含吉凶结果（如“大吉”、“凶”等）以及对应的运势描述。 @Shouugou
13. astrbot_plugin_videosummary - 使用 bibigpt 实现视频总结 @kterna
14. astrbot_plugin_InitiativeDialogue - 使 bot 在用户长时间未发送消息时主动与用户对话的插件 @advent259141
15. astrbot_plugin_emoji - 基于达莉娅综合群娱插件的表情包制作插件，仅保留了@其他群员制作表情包的部分。由桑帛云API提供表情包制作。@KurisuRee7
16. astrbot_plugin_videos_analysis - 聚合视频分享链接解析（仅测试过napcat） @miaoxutao123
17. astrbot_plugin_daily_news - 每日 60 秒新闻推送插件 - 自动推送每日热点新闻 @anka-afk