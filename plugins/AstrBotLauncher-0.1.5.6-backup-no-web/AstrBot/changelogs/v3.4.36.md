# What's Changed

1. ✨ 新增: 支持插件会话控制 API
2. ✨ 新增: add template of LMStudio #691
3. ✨ 新增: 更好的插件卡片的 UI，插件卡片支持显示 logo，推荐插件页面 
4. ✨ 新增: 支持当消息只有 @bot 时，下一条发送人的消息**直接唤醒机器人** #714
5.  ⚡ 优化: Webchat 和 Gewechat 的图片、语音等主动消息发送 #710
6.  ⚡ 优化: 完善了插件的启用和禁用的生命周期管理
7.  ⚡ 优化: 安装插件/更新插件/保存插件配置后直接热重载而不重启；优化了 plugin 指令
8.  🐛 修复: 主动人格情况下人格失效的问题 #719 #712
9.  🐛 修复: 404 error after installing plugins
10. 🐛 修复: telegram cannot handle /start #620
11. 🐛 修复: 修复插件在带了 __del__ 之后无法被禁用和重载的问题
12. 🐛 修复: context.get_platform() error
13. 🐛 修复: Telegram 适配器使用代理地址无法获取图片 #723