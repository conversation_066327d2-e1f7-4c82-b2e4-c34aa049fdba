[project]
name = "AstrBot"
version = "3.5.24"
description = "易上手的多平台 LLM 聊天机器人及开发框架"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiocqhttp>=1.4.4",
    "aiodocker>=0.24.0",
    "aiohttp>=3.11.18",
    "aiosqlite>=0.21.0",
    "anthropic>=0.51.0",
    "apscheduler>=3.11.0",
    "beautifulsoup4>=4.13.4",
    "certifi>=2025.4.26",
    "chardet~=5.1.0",
    "colorlog>=6.9.0",
    "cryptography>=44.0.3",
    "dashscope>=1.23.2",
    "defusedxml>=0.7.1",
    "dingtalk-stream>=0.22.1",
    "docstring-parser>=0.16",
    "faiss-cpu>=1.10.0",
    "filelock>=3.18.0",
    "google-genai>=1.14.0",
    "googlesearch-python>=1.3.0",
    "lark-oapi>=1.4.15",
    "lxml-html-clean>=0.4.2",
    "mcp>=1.8.0",
    "openai>=1.78.0",
    "ormsgpack>=1.9.1",
    "pillow>=11.2.1",
    "pip>=25.1.1",
    "psutil>=5.8.0",
    "py-cord>=2.6.1",
    "pydantic~=2.10.3",
    "pydub>=0.25.1",
    "pyjwt>=2.10.1",
    "python-telegram-bot>=22.0",
    "qq-botpy>=1.2.1",
    "quart>=0.20.0",
    "readability-lxml>=*******",
    "silk-python>=0.2.6",
    "slack-sdk>=3.35.0",
    "telegramify-markdown>=0.5.1",
    "watchfiles>=1.0.5",
    "websockets>=15.0.1",
    "wechatpy>=1.8.18",
]

[project.scripts]
astrbot = "astrbot.cli.__main__:cli"

[build-system]
requires = ["hatchling", "uv-dynamic-versioning"]
build-backend = "hatchling.build"

[tool.ruff]
exclude = [
  "astrbot/core/utils/t2i/local_strategy.py",
  "astrbot/api/all.py",
]
line-length = 88
lint.ignore = [
    "F403", 
    "F405", 
    "E501",
    "ASYNC230" # TODO: handle ASYNC230 in AstrBot
]
lint.select = [
  "F", # Pyflakes
  "W", # pycodestyle warnings
  "E", # pycodestyle errors
  "ASYNC", # flake8-async
  "C4", # flake8-comprehensions
  "Q", # flake8-quotes
]
target-version = "py310"
