# -*- coding: utf-8 -*-
"""
AstrBotLauncher 插件主入口
- 继承MaiBot BasePlugin
- 注册Action/Command/Tool等组件（稍后补充）
"""
from __future__ import annotations

from typing import List, Tuple, Type

from src.plugin_system.base import BasePlugin, register_plugin
from src.plugin_system.base.component_types import ComponentInfo


@register_plugin
class AstrBotLauncherPlugin(BasePlugin):
    """AstrBot 与 MaiBot 深度集成插件主类。"""

    plugin_name = "astrbot_launcher"
    plugin_description = "AstrBot 深度集成插件：进程管理、消息桥接、Star桥接、IPC通信等"
    plugin_version = "*******"
    plugin_author = "AstrBot Integration Team"
    enable_plugin = True
    config_file_name = "astrbot_config.toml"  # 后续生成

    # 配置Schema定义（MaiBot规范：使用ConfigField）
    from src.plugin_system.base.config_types import ConfigField

    config_schema = {
        "plugin": {
            "enabled": ConfigField(
                type=bool,
                default=True,
                description="是否启用AstrBot集成插件",
            )
        },
        "astrbot": {
            "python_executable": ConfigField(type=str, default="python", description="Python解释器路径"),
            "working_directory": ConfigField(type=str, default="./AstrBot", description="AstrBot工作目录"),
            "entry_point": ConfigField(type=str, default="./AstrBot/main.py", description="AstrBot主脚本路径"),
            "ipc_port": ConfigField(type=int, default=8081, description="IPC端口(仅本机)"),
            "auto_restart": ConfigField(type=bool, default=True, description="子进程异常自动重启")
        }
    }

    def __init__(self, plugin_dir: str = None):
        """初始化AstrBot集成插件，创建核心组件实例。"""
        super().__init__(plugin_dir)

        # 初始化核心组件
        from pathlib import Path
        from .components.process_manager import AstrBot进程管理器
        from .components.star_bridge import Star桥接器
        from .components.ipc_client import AstrBotIPC客户端
        from src.common.logger import get_logger

        self._logger = get_logger("AstrBotLauncher")
        插件根 = Path(plugin_dir) if plugin_dir else Path(__file__).parent

        self.进程管理器 = AstrBot进程管理器(插件根)
        self.star桥接器 = Star桥接器(插件根, self.plugin_name)
        self.ipc客户端 = AstrBotIPC客户端(插件根)

        # 动态组件列表（由Star桥接器生成）
        self._动态组件列表: List[Tuple[ComponentInfo, Type]] = []

    async def 启动组件(self):
        """启动所有组件。"""
        try:
            # 启动AstrBot进程
            await self.进程管理器.启动()
            # 连接IPC
            await self.ipc客户端.连接()
            # 扫描并注册Star插件
            self._动态组件列表 = await self.star桥接器.扫描并注册()
            self._logger.info(f"成功启动AstrBot集成，发现 {len(self._动态组件列表)} 个Star组件")
        except Exception as e:
            self._logger.error(f"启动AstrBot集成组件失败: {e}")

    async def 停止组件(self):
        """停止所有组件。"""
        try:
            await self.star桥接器.卸载全部()
            await self.ipc客户端.停止()
            await self.进程管理器.停止()
            self._logger.info("AstrBot集成组件已停止")
        except Exception as e:
            self._logger.error(f"停止AstrBot集成组件失败: {e}")

    def get_plugin_components(self) -> List[Tuple[ComponentInfo, Type]]:
        """返回插件包含的组件列表（包括动态生成的Star组件和静态命令组件）。"""
        # 静态命令组件
        from .commands.astrbot_commands import (
            AstrBotStatusCommand, AstrBotRestartCommand,
            AstrBotPluginsCommand, AstrBotReloadCommand
        )
        from src.plugin_system.base.component_types import CommandInfo, ComponentType

        静态组件列表 = [
            (CommandInfo(
                name="astr_status",
                component_type=ComponentType.COMMAND,
                description="查询AstrBot运行状态",
                command_pattern=r"^/astr\s+status$",
                intercept_message=True,
                plugin_name=self.plugin_name,
            ), AstrBotStatusCommand),
            (CommandInfo(
                name="astr_restart",
                component_type=ComponentType.COMMAND,
                description="重启AstrBot进程",
                command_pattern=r"^/astr\s+restart$",
                intercept_message=True,
                plugin_name=self.plugin_name,
            ), AstrBotRestartCommand),
            (CommandInfo(
                name="astr_plugins",
                component_type=ComponentType.COMMAND,
                description="列出AstrBot插件状态",
                command_pattern=r"^/astr\s+plugins$",
                intercept_message=True,
                plugin_name=self.plugin_name,
            ), AstrBotPluginsCommand),
            (CommandInfo(
                name="astr_reload",
                component_type=ComponentType.COMMAND,
                description="热重载Star插件",
                command_pattern=r"^/astr\s+reload$",
                intercept_message=True,
                plugin_name=self.plugin_name,
            ), AstrBotReloadCommand),
        ]

        # 返回静态组件 + 动态Star组件
        return 静态组件列表 + self._动态组件列表.copy()

