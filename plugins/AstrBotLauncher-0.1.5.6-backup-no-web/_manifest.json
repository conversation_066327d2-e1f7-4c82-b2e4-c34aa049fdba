{"name": "AstrBotLauncher", "version": "*******", "description": "AstrBot 与 MaiBot 深度集成插件：进程管理、消息桥接、Star桥接、IPC通信等", "author": {"name": "AstrBot Integration Team", "email": "<EMAIL>"}, "homepage_url": "https://github.com/astrbotdevs/astrbot", "repository_url": "https://github.com/astrbotdevs/astrbot-maibot-integration", "keywords": ["<PERSON><PERSON><PERSON>", "ma<PERSON>ot", "integration", "plugin", "ipc"], "categories": ["Integration", "AI", "<PERSON><PERSON><PERSON>"], "default_locale": "zh-CN", "locales_path": "_locales", "plugin_info": {"is_built_in": false, "plugin_type": "launcher", "components": [{"type": "action", "name": "astrbot_process_action", "description": "管理AstrBot子进程生命周期"}, {"type": "action", "name": "astrbot_message_action", "description": "桥接AstrBot消息处理"}, {"type": "command", "name": "astrbot_status", "description": "查询AstrBot运行状态", "pattern": "/astr status"}], "features": ["进程管理", "消息适配", "Star桥接", "IPC通信", "性能监控", "错误恢复"]}}