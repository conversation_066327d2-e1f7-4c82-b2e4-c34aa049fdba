# -*- coding: utf-8 -*-
"""
AstrBot控制命令组件
- 提供基础的AstrBot管理命令
- 继承MaiBot BaseCommand规范
"""
from __future__ import annotations

import asyncio
from pathlib import Path
from typing import Any, Dict

from src.plugin_system.base.base_command import BaseCommand
from src.plugin_system.base.component_types import CommandInfo, ComponentType
from src.common.logger import get_logger


class AstrBotStatusCommand(BaseCommand):
    """查询AstrBot运行状态命令。"""
    
    command_name = "astr_status"
    command_description = "查询AstrBot运行状态和连接状态"
    command_pattern = r"^/astr\s+status$"
    intercept_message = True

    def __init__(self, message, plugin_config=None):
        super().__init__(message, plugin_config)
        self._logger = get_logger("AstrBotStatus")

    async def execute(self):
        """执行状态查询命令。"""
        try:
            # 获取AstrBot集成插件实例
            from src.plugin_system.core.plugin_manager import plugin_manager
            plugin_instance = plugin_manager.get_plugin_instance("astrbot_launcher")
            
            if not plugin_instance:
                return False, "AstrBot集成插件未加载"
            
            # 查询各组件状态
            进程状态 = "运行中" if plugin_instance.进程管理器._proc else "未启动"
            ipc状态 = "已连接" if plugin_instance.ipc客户端._connected_evt.is_set() else "未连接"
            star组件数 = len(plugin_instance._动态组件列表)
            
            状态报告 = f"""
🤖 AstrBot 集成状态报告

📊 核心状态：
• 进程状态：{进程状态}
• IPC连接：{ipc状态}
• Star组件：{star组件数} 个

🔧 组件详情：
• 进程管理器：✅ 已加载
• IPC客户端：✅ 已加载
• Star桥接器：✅ 已加载

💡 使用 /astr help 查看更多命令
            """.strip()
            
            return True, 状态报告
            
        except Exception as e:
            self._logger.error(f"查询AstrBot状态失败: {e}")
            return False, f"查询状态失败: {e}"


class AstrBotRestartCommand(BaseCommand):
    """重启AstrBot进程命令。"""
    
    command_name = "astr_restart"
    command_description = "重启AstrBot进程"
    command_pattern = r"^/astr\s+restart$"
    intercept_message = True

    def __init__(self, message, plugin_config=None):
        super().__init__(message, plugin_config)
        self._logger = get_logger("AstrBotRestart")

    async def execute(self):
        """执行重启命令。"""
        try:
            from src.plugin_system.core.plugin_manager import plugin_manager
            plugin_instance = plugin_manager.get_plugin_instance("astrbot_launcher")
            
            if not plugin_instance:
                return False, "AstrBot集成插件未加载"
            
            # 执行重启
            await plugin_instance.进程管理器.停止()
            await asyncio.sleep(2)  # 等待进程完全停止
            await plugin_instance.进程管理器.启动()
            
            return True, "🔄 AstrBot 进程重启成功"
            
        except Exception as e:
            self._logger.error(f"重启AstrBot失败: {e}")
            return False, f"重启失败: {e}"


class AstrBotPluginsCommand(BaseCommand):
    """列出AstrBot插件状态命令。"""
    
    command_name = "astr_plugins"
    command_description = "列出AstrBot插件状态"
    command_pattern = r"^/astr\s+plugins$"
    intercept_message = True

    def __init__(self, message, plugin_config=None):
        super().__init__(message, plugin_config)
        self._logger = get_logger("AstrBotPlugins")

    async def execute(self):
        """执行插件列表查询。"""
        try:
            from src.plugin_system.core.plugin_manager import plugin_manager
            plugin_instance = plugin_manager.get_plugin_instance("astrbot_launcher")
            
            if not plugin_instance:
                return False, "AstrBot集成插件未加载"
            
            # 通过IPC查询插件列表
            插件列表 = await plugin_instance.ipc客户端.列出插件()
            
            插件报告 = "📦 AstrBot 插件列表：\n\n"
            if plugin_instance._动态组件列表:
                for i, (组件信息, _) in enumerate(plugin_instance._动态组件列表, 1):
                    插件报告 += f"{i}. {组件信息.name} - {组件信息.description}\n"
            else:
                插件报告 += "暂无已桥接的Star插件"
            
            return True, 插件报告
            
        except Exception as e:
            self._logger.error(f"查询插件列表失败: {e}")
            return False, f"查询插件失败: {e}"


class AstrBotReloadCommand(BaseCommand):
    """热重载Star插件命令。"""
    
    command_name = "astr_reload"
    command_description = "热重载Star插件"
    command_pattern = r"^/astr\s+reload$"
    intercept_message = True

    def __init__(self, message, plugin_config=None):
        super().__init__(message, plugin_config)
        self._logger = get_logger("AstrBotReload")

    async def execute(self):
        """执行热重载命令。"""
        try:
            from src.plugin_system.core.plugin_manager import plugin_manager
            plugin_instance = plugin_manager.get_plugin_instance("astrbot_launcher")
            
            if not plugin_instance:
                return False, "AstrBot集成插件未加载"
            
            # 执行热重载
            旧组件数 = len(plugin_instance._动态组件列表)
            plugin_instance._动态组件列表 = await plugin_instance.star桥接器.热重载()
            新组件数 = len(plugin_instance._动态组件列表)
            
            return True, f"🔄 Star插件热重载完成\n旧组件数：{旧组件数}\n新组件数：{新组件数}"
            
        except Exception as e:
            self._logger.error(f"热重载失败: {e}")
            return False, f"热重载失败: {e}"


# 导出所有命令类
__all__ = [
    "AstrBotStatusCommand",
    "AstrBotRestartCommand", 
    "AstrBotPluginsCommand",
    "AstrBotReloadCommand"
]
