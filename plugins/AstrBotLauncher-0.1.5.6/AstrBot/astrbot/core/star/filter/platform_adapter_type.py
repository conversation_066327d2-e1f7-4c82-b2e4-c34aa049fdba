# -*- coding: utf-8 -*-
"""
平台适配器类型过滤器

注意：平台管理器已移除，此文件保留基本类型定义以保持插件系统兼容性
"""

from enum import Enum
from typing import Any

from astrbot.core.star.filter.custom_filter import CustomFilter
from astrbot.core.platform.astr_message_event import AstrMessageEvent
from astrbot.core.config import AstrBotConfig


class PlatformAdapterType(Enum):
    """
    平台适配器类型枚举
    
    注意：虽然平台管理器已移除，但保留这些类型定义以维持插件兼容性
    """
    # 特殊类型
    ALL = "all"  # 匹配所有平台类型
    UNKNOWN = "unknown"
    
    # 以下为历史兼容性保留的平台类型
    QQ_OFFICIAL = "qq_official"
    QQ_AIOCQHTTP = "aiocqhttp" 
    TELEGRAM = "telegram"
    DISCORD = "discord"
    WECHAT = "wechat"
    DINGTALK = "dingtalk"
    SLACK = "slack"
    LARK = "lark"  # 飞书


class PlatformAdapterTypeFilter(CustomFilter):
    """
    平台适配器类型过滤器
    
    注意：由于平台管理器已移除，此过滤器将始终返回False，
    但保留接口以维持插件系统兼容性
    """
    
    def __init__(self, platform_adapter_type: PlatformAdapterType):
        """
        初始化平台适配器类型过滤器
        
        Args:
            platform_adapter_type: 平台适配器类型
        """
        self.platform_adapter_type = platform_adapter_type
    
    def filter(self, event: AstrMessageEvent, cfg: AstrBotConfig) -> bool:
        """
        过滤器执行函数
        
        注意：由于平台管理器已移除，对于ALL类型返回True以保持插件兼容性，
        其他平台类型返回False
        
        Args:
            event: 消息事件
            cfg: 配置对象
            
        Returns:
            bool: ALL类型返回True，其他返回False
        """
        # 如果是ALL类型，返回True以匹配所有情况
        if self.platform_adapter_type == PlatformAdapterType.ALL:
            return True
        
        # 其他平台类型由于平台管理器已移除，返回False
        return False
    
    def __str__(self) -> str:
        return f"PlatformAdapterTypeFilter({self.platform_adapter_type.value})"
