{"name": "MaiBot-Napcat-Adapter-DevContainer", "image": "mcr.microsoft.com/devcontainers/python:1-3.12-bullseye", "features": {"ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {"packages": ["tmux"]}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "forwardPorts": ["8095:8095"], "postCreateCommand": "pip3 install --user -r requirements.txt", "customizations": {"jetbrains": {"backend": "PyCharm"}}}