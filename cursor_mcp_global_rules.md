# Cursor MCP 全局调用规则文档

## 📋 规则总览 (Rules Overview)

本文档定义了在 Cursor IDE 中使用 MCP (Model Context Protocol) 工具的全局规则和标准。

---

## 🔤 编码和注释规范 (Encoding & Comments Standards)

### 1. 字符编码要求

```python
# -*- coding: utf-8 -*-
# 所有Python文件必须使用UTF-8编码
# 文件开头必须包含编码声明
```

### 2. 中文注释规范

```python
def function_name(param1: str, param2: int) -> bool:
    """函数功能描述（必须使用中文）
    
    Args:
        param1 (str): 参数1描述（中文）
        param2 (int): 参数2描述（中文）
        
    Returns:
        bool: 返回值描述（中文）
        
    Raises:
        ValueError: 异常描述（中文）
        
    Example:
        >>> result = function_name("测试", 123)
        >>> print(result)  # 输出结果说明
    """
    # 行内注释必须使用中文
    pass
```

### 3. 日志和输出规范

```python
import logging

# 配置中文日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

logger = logging.getLogger(__name__)
logger.info("这是中文日志信息")
print("控制台输出必须支持中文显示")
```

---

## 🎯 任务执行规范 (Task Execution Standards)

### 1. 任务驱动开发模式

```python
# 每个用户请求都必须转换为具体的执行任务
class TaskProcessor:
    """任务处理器 - 将用户请求转换为可执行任务"""
    
    def __init__(self):
        self.tasks = []
        self.completed_tasks = []
        self.test_files = []  # 跟踪生成的测试文件
    
    def create_task(self, user_request: str) -> dict:
        """将用户请求转换为任务"""
        task = {
            'id': self.generate_task_id(),
            'description': user_request,
            'status': 'pending',
            'steps': self.parse_request_to_steps(user_request),
            'validation_criteria': self.define_validation_criteria(user_request),
            'cleanup_files': []  # 需要清理的文件列表
        }
        return task
    
    def execute_task(self, task: dict) -> bool:
        """执行任务"""
        try:
            # 1. 使用对应的MCP工具执行
            mcp_result = self.use_appropriate_mcp_tools(task)
            
            # 2. 验证执行结果
            validation_result = self.validate_execution(task, mcp_result)
            
            # 3. 清理测试文件
            self.cleanup_test_files(task)
            
            return validation_result
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            self.cleanup_test_files(task)  # 确保清理
            return False
```

### 2. 验证和清理机制

```python
def validate_and_cleanup(task_result, test_files_list):
    """验证任务结果并清理测试文件"""
    
    # Step 1: 验证功能完整性
    validation_passed = True
    
    # 验证代码功能
    if not verify_code_functionality(task_result):
        validation_passed = False
        logger.error("代码功能验证失败")
    
    # 验证中文编码支持
    if not verify_chinese_encoding(task_result):
        validation_passed = False
        logger.error("中文编码支持验证失败")
    
    # Step 2: 强制清理所有测试文件
    cleanup_success = True
    for test_file in test_files_list:
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
                logger.info(f"已清理测试文件: {test_file}")
        except Exception as e:
            logger.error(f"清理测试文件失败 {test_file}: {e}")
            cleanup_success = False
    
    # Step 3: 清理临时目录
    temp_dirs = [d for d in os.listdir('.') if d.startswith('temp_') or d.startswith('test_')]
    for temp_dir in temp_dirs:
        try:
            if os.path.isdir(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            logger.error(f"清理临时目录失败 {temp_dir}: {e}")
    
    return validation_passed and cleanup_success
```

---

## 🚫 代码质量禁令 (Code Quality Prohibitions)

### 1. 绝对禁止的行为

```python
# ❌ 绝对禁止 - 简化代码
def bad_function():
    pass  # 这种空实现是被禁止的

# ❌ 绝对禁止 - 虚假功能
def fake_database_connection():
    print("模拟数据库连接")  # 不允许模拟功能
    return "fake_connection"

# ❌ 绝对禁止 - 无关功能建议
def suggest_unrelated_feature():
    """不允许建议与项目无关的功能"""
    pass

# ✅ 必须的实现方式 - 完整功能
def real_database_connection(host: str, port: int, database: str) -> object:
    """真实的数据库连接实现"""
    try:
        import sqlite3  # 或其他真实数据库驱动
        connection = sqlite3.connect(f"{database}.db")
        logger.info(f"成功连接到数据库: {database}")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise ConnectionError(f"无法连接到数据库: {e}")
```

### 2. 代码质量标准

```python
class CodeQualityStandards:
    """代码质量标准定义"""
    
    @staticmethod
    def validate_code_requirements(code_block: str) -> dict:
        """验证代码是否符合要求"""
        
        requirements = {
            'has_real_implementation': False,
            'has_chinese_comments': False,
            'has_error_handling': False,
            'has_logging': False,
            'no_placeholder_code': True,
            'no_mock_functions': True
        }
        
        # 检查真实实现
        if 'pass' not in code_block or '# TODO' not in code_block:
            requirements['has_real_implementation'] = True
        
        # 检查中文注释
        chinese_pattern = r'[\u4e00-\u9fff]'
        if re.search(chinese_pattern, code_block):
            requirements['has_chinese_comments'] = True
        
        # 检查错误处理
        if 'try:' in code_block and 'except' in code_block:
            requirements['has_error_handling'] = True
        
        # 检查日志记录
        if 'logger.' in code_block or 'logging.' in code_block:
            requirements['has_logging'] = True
        
        # 检查占位符代码
        placeholder_patterns = ['pass', 'TODO', '...', 'NotImplemented']
        for pattern in placeholder_patterns:
            if pattern in code_block:
                requirements['no_placeholder_code'] = False
        
        # 检查模拟函数
        mock_patterns = ['mock', 'fake', 'dummy', '模拟', '虚假']
        for pattern in mock_patterns:
            if pattern.lower() in code_block.lower():
                requirements['no_mock_functions'] = False
        
        return requirements
```

---

## 🛠️ MCP工具使用规范 (MCP Tools Usage Standards)

### 1. 强制MCP工具调用

```python
class MCPToolManager:
    """MCP工具管理器 - 确保每次操作都使用对应的MCP工具"""
    
    REQUIRED_TOOLS = {
        'file_operations': 'filesystem',
        'code_search': 'codebase_search', 
        'browser_automation': 'playwright',
        'system_commands': 'desktop-commander',
        'thinking_process': 'sequential-thinking',
        'documentation': 'context7',
        'memory_management': 'memory',
        'data_collection': 'brightdata',
        'comprehensive_tasks': 'everything'
    }
    
    def __init__(self):
        self.tool_usage_log = []
    
    def ensure_mcp_tool_usage(self, operation_type: str, operation_details: dict):
        """确保使用对应的MCP工具"""
        
        required_tool = self.REQUIRED_TOOLS.get(operation_type)
        if not required_tool:
            raise ValueError(f"未找到操作类型 {operation_type} 对应的MCP工具")
        
        # 记录工具使用
        usage_record = {
            'timestamp': datetime.now().isoformat(),
            'operation_type': operation_type,
            'mcp_tool': required_tool,
            'operation_details': operation_details
        }
        
        self.tool_usage_log.append(usage_record)
        logger.info(f"使用MCP工具: {required_tool} 执行操作: {operation_type}")
        
        # 调用对应的MCP工具
        return self.call_mcp_tool(required_tool, operation_details)
    
    def call_mcp_tool(self, tool_name: str, parameters: dict):
        """调用指定的MCP工具"""
        try:
            # 这里实现具体的MCP工具调用逻辑
            # 每个工具都有其特定的调用方式
            
            if tool_name == 'filesystem':
                return self.call_filesystem_tool(parameters)
            elif tool_name == 'codebase_search':
                return self.call_codebase_search_tool(parameters)
            elif tool_name == 'playwright':
                return self.call_playwright_tool(parameters)
            # ... 其他工具的调用实现
            
        except Exception as e:
            logger.error(f"MCP工具调用失败 {tool_name}: {e}")
            raise
```

### 2. 工具调用验证机制

```python
def validate_mcp_tool_usage():
    """验证MCP工具使用情况"""
    
    # 检查每个操作是否都使用了对应的MCP工具
    operations_log = get_current_session_operations()
    
    for operation in operations_log:
        if not operation.get('used_mcp_tool'):
            raise ComplianceError(
                f"操作 {operation['type']} 未使用对应的MCP工具，违反全局规则"
            )
    
    logger.info("MCP工具使用验证通过")
    return True
```

---

## 🔄 项目真实性验证 (Project Authenticity Verification)

### 1. 项目代码真实性检查

```python
def verify_project_authenticity(code_changes: list) -> bool:
    """验证代码更改的真实性"""
    
    # 读取项目真实的代码结构
    project_structure = analyze_project_structure()
    
    for change in code_changes:
        # 验证更改是否基于真实的项目文件
        if not is_based_on_real_project_files(change, project_structure):
            logger.error(f"检测到虚假代码更改: {change['file']}")
            return False
        
        # 验证功能是否与项目实际需求相符
        if not matches_project_requirements(change):
            logger.error(f"代码更改与项目需求不符: {change['description']}")
            return False
    
    return True

def analyze_project_structure() -> dict:
    """分析项目真实结构"""
    structure = {
        'main_files': [],
        'modules': [],
        'config_files': [],
        'dependencies': []
    }
    
    # 使用filesystem MCP工具扫描项目
    # 必须使用MCP工具，不能直接使用os.walk等
    
    return structure
```

### 2. 功能实现真实性验证

```python
def verify_functionality_authenticity(function_implementation: str) -> bool:
    """验证功能实现的真实性"""
    
    authenticity_checks = {
        'has_real_imports': check_real_imports(function_implementation),
        'has_actual_logic': check_actual_logic(function_implementation),
        'integrates_with_project': check_project_integration(function_implementation),
        'no_placeholder_implementations': check_no_placeholders(function_implementation)
    }
    
    all_checks_passed = all(authenticity_checks.values())
    
    if not all_checks_passed:
        failed_checks = [k for k, v in authenticity_checks.items() if not v]
        logger.error(f"功能真实性验证失败: {failed_checks}")
    
    return all_checks_passed
```

---

## 📝 全局执行检查清单 (Global Execution Checklist)

### 任务执行前检查

- [ ] 确认使用UTF-8编码
- [ ] 确认所有注释使用中文
- [ ] 确认任务具体可执行
- [ ] 确认选择了对应的MCP工具

### 任务执行中检查

- [ ] 实时验证代码功能性
- [ ] 确保没有占位符代码
- [ ] 确保没有虚假功能实现
- [ ] 记录生成的测试文件

### 任务执行后检查

- [ ] 验证功能完整性
- [ ] 验证中文编码支持
- [ ] 清理所有测试文件
- [ ] 清理临时目录
- [ ] 验证MCP工具使用记录

---

## ⚠️ 违规处理机制 (Violation Handling)

```python
class ComplianceViolationError(Exception):
    """合规违规错误"""
    pass

def handle_rule_violation(violation_type: str, details: str):
    """处理规则违规"""
    
    error_message = f"违反全局规则 - {violation_type}: {details}"
    logger.error(error_message)
    
    # 立即停止当前操作
    cleanup_all_temporary_files()
    
    # 抛出合规错误
    raise ComplianceViolationError(error_message)

def enforce_global_rules():
    """强制执行全局规则"""
    
    # 检查编码规范
    if not verify_encoding_compliance():
        handle_rule_violation("编码规范", "未使用UTF-8编码或缺少中文注释")
    
    # 检查MCP工具使用
    if not verify_mcp_tool_usage():
        handle_rule_violation("MCP工具使用", "未使用对应的MCP工具")
    
    # 检查代码质量
    if not verify_code_quality():
        handle_rule_violation("代码质量", "存在简化代码或虚假功能")
    
    # 检查清理完成
    if not verify_cleanup_completion():
        handle_rule_violation("清理机制", "未完成测试文件清理")
```

---

## 🎯 总结 (Summary)

本全局规则文档确保：

1. **中文优先**: 所有注释、日志、输出必须使用中文
2. **任务驱动**: 每个请求都转换为具体可执行的任务
3. **MCP强制**: 每个操作都必须使用对应的MCP工具
4. **真实代码**: 绝不允许简化、虚假、模拟的代码实现
5. **自动清理**: 执行完毕后自动清理所有测试文件
6. **严格验证**: 多层次验证确保代码质量和功能真实性

**违反任何规则都将导致操作立即终止并进行强制清理。**

---

*此文档为Cursor IDE中MCP工具使用的全局标准，适用于所有项目和操作。*
