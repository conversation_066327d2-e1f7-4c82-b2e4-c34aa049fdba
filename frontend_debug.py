#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AstrBot Dashboard 前端白屏问题深度诊断工具
专门用于诊断Vue.js应用白屏问题的企业级诊断工具
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import re

async def deep_frontend_diagnosis():
    """深度前端白屏问题诊断"""
    
    print('=== AstrBot Dashboard 前端白屏问题深度诊断 ===')
    print('诊断时间:', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print('目标: 找出Vue.js应用白屏的根本原因')
    print()
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # 1. 检查主页HTML内容
            print('1. 主页HTML内容分析:')
            print('-' * 50)
            
            async with session.get('http://127.0.0.1:6185/', timeout=10) as response:
                if response.status == 200:
                    html_content = await response.text()
                    print(f'✅ HTML加载成功: {len(html_content)} 字符')
                    
                    # 检查关键元素
                    if '<div id="app"></div>' in html_content:
                        print('✅ Vue挂载点存在: <div id="app"></div>')
                    else:
                        print('❌ Vue挂载点缺失!')
                    
                    if 'integration-mode-fake-token' in html_content:
                        print('✅ 集成模式脚本存在')
                    else:
                        print('❌ 集成模式脚本缺失!')
                    
                    # 检查JavaScript文件引用
                    js_match = re.search(r'src="/assets/(index-[a-f0-9]+\.js)"', html_content)
                    if js_match:
                        js_file = js_match.group(1)
                        print(f'✅ JavaScript文件引用: {js_file}')

                        # 检查是否为最新构建的文件
                        if 'index-0bfbb596.js' in js_file:
                            print('✅ 使用最新构建的JavaScript文件（修复i18n问题后）')
                        else:
                            print(f'⚠️  JavaScript文件可能不是最新版本: {js_file}')
                    else:
                        print('❌ JavaScript文件引用缺失!')
                        
                    # 检查CSS文件引用
                    css_match = re.search(r'href="/assets/(index-[a-f0-9]+\.css)"', html_content)
                    if css_match:
                        css_file = css_match.group(1)
                        print(f'✅ CSS文件引用: {css_file}')
                    else:
                        print('❌ CSS文件引用缺失!')
                        
                else:
                    print(f'❌ HTML加载失败: {response.status}')
                    return
            
            print()
            print('2. JavaScript文件内容检查:')
            print('-' * 50)
            
            # 检查JavaScript文件
            if js_file:
                async with session.get(f'http://127.0.0.1:6185/assets/{js_file}', timeout=10) as response:
                    if response.status == 200:
                        js_content = await response.text()
                        print(f'✅ JavaScript文件加载成功: {len(js_content)} 字符')
                        
                        # 检查关键Vue.js内容
                        if 'createApp' in js_content or 'Vue' in js_content:
                            print('✅ Vue.js应用代码存在')
                        else:
                            print('❌ Vue.js应用代码缺失!')

                        if 'createRouter' in js_content:
                            print('✅ Vue Router代码存在')
                        else:
                            print('❌ Vue Router代码缺失!')

                        # 检查集成模式相关代码
                        if 'AstrBot集成模式' in js_content:
                            print('✅ 集成模式初始化代码存在于JS中')
                        elif 'integration-mode-fake-token' in js_content:
                            print('✅ 集成模式逻辑存在于JS中')
                        else:
                            print('⚠️  集成模式逻辑不在JS中（应该在HTML中）')
                            
                    else:
                        print(f'❌ JavaScript文件加载失败: {response.status}')
            
            print()
            print('3. 关键API端点检查:')
            print('-' * 50)
            
            # 检查仪表盘可能需要的API
            critical_apis = [
                '/api/stat/get',
                '/api/config/get',
                '/api/plugin/list'
            ]
            
            for api in critical_apis:
                try:
                    async with session.get(f'http://127.0.0.1:6185{api}', timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f'✅ {api}: 正常 ({response.status})')
                        else:
                            print(f'❌ {api}: 错误 ({response.status})')
                except Exception as e:
                    print(f'❌ {api}: 异常 - {str(e)}')
            
            print()
            print('4. 路由访问测试:')
            print('-' * 50)
            
            # 测试不同的路由路径
            test_routes = [
                '/',
                '/#/',
                '/#/main',
                '/#/main/dashboard',
                '/#/main/dashboard/default'
            ]
            
            for route in test_routes:
                try:
                    async with session.get(f'http://127.0.0.1:6185{route}', timeout=5) as response:
                        if response.status == 200:
                            content = await response.text()
                            print(f'✅ {route}: 正常 ({len(content)} 字符)')
                        else:
                            print(f'❌ {route}: 错误 ({response.status})')
                except Exception as e:
                    print(f'❌ {route}: 异常 - {str(e)}')
            
            print()
            print('5. 诊断结论和建议:')
            print('-' * 50)
            
            # 基于检查结果给出诊断结论
            print('🔍 可能的白屏原因:')
            print('1. Vue.js应用初始化失败')
            print('2. 路由配置问题导致组件无法加载')
            print('3. JavaScript执行错误阻止了应用渲染')
            print('4. CSS样式问题导致内容不可见')
            print('5. API请求失败导致数据加载问题')
            
            print()
            print('💡 建议的解决步骤:')
            print('1. 使用浏览器开发者工具检查Console错误')
            print('2. 检查Network面板确认所有资源正常加载')
            print('3. 验证Vue应用是否成功挂载到#app元素')
            print('4. 检查路由守卫是否正确处理集成模式')
            
    except Exception as e:
        print(f'❌ 诊断过程发生错误: {e}')
    
    print()
    print('=== 深度诊断完成 ===')

if __name__ == '__main__':
    asyncio.run(deep_frontend_diagnosis())
