一键包使用教程(数据迁移教程在下面):

首先,运行  "![第一次启动点我！！！.bat"  这个文件
它会自动帮你寻找合适的Python环境，配置环境

这个脚本运行完成(即完成机器人QQ号输入, 初步简易配置之后)
启动  "![启动点我！！！.bat"  这个脚本

这时, 会打开麦麦一键包控制台
全中文操作, 看不懂我也没办法, 默认全部启动选1


LPMM知识库导入向导：
https://docs.mai-mai.org/manual/deployment/LPMM.html#%E9%BA%A6%E9%BA%A6%E5%AD%A6%E4%B9%A0%E7%9F%A5%E8%AF%86

0.6升级到0.7说明：https://docs.mai-mai.org/faq/maibot/update_to_07.html

麦麦配置帮助，包括如何启用禁言，配置风格：https://docs.mai-mai.org/faq/maibot/settings.html

配置文件详解：https://docs.mai-mai.org/manual/configuration/
 
插件编写指南：https://docs.mai-mai.org/develop/plugin_develop/


数据迁移教程：

从旧版一键包迁移：

1.迁移配置文件：

注意！！！！配置文件在更新版本时可能会有非常大的改动！！

推荐方法：打开新旧配置文件，对照着一个一个复制

需要迁移的配置文件：
bot_config.toml
lpmm_config.toml
.env

