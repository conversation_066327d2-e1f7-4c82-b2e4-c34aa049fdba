<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="AstrBot Soulter" />
    <meta name="description" content="AstrBot Dashboard" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
    />
    <title>AstrBot - 仪表盘（集成模式）</title>
    <script type="module" crossorigin src="/assets/index-c41ea1c5.js"></script>
    <link rel="stylesheet" href="/assets/index-c3397e00.css">

    <!-- 集成模式：自动设置假token绕过认证 -->
    <script>
      console.log('🔗 AstrBot集成模式：初始化无认证访问');

      // 在Vue应用加载前设置假token
      localStorage.setItem('token', 'integration-mode-fake-token');
      localStorage.setItem('username', 'integration-user');

      // 调试信息
      console.log('Token设置完成:', localStorage.getItem('token'));
      console.log('当前URL:', window.location.href);
      console.log('当前Hash:', window.location.hash);

      // 简化的重定向逻辑
      function checkAndRedirect() {
        const hash = window.location.hash;
        console.log('检查Hash:', hash);

        // 只在特定情况下重定向
        if (hash === '' || hash === '#/' || hash === '#/auth/login') {
          console.log('🔗 重定向到仪表盘');
          window.location.hash = '#/main/dashboard/default';
        }
      }

      // 页面加载时执行
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🔗 DOM加载完成');
        setTimeout(checkAndRedirect, 100);
      });

      // 确保token始终存在
      setInterval(function() {
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'integration-mode-fake-token');
          localStorage.setItem('username', 'integration-user');
        }
      }, 1000);
    </script>
  </head>
  <body>
    <div id="app">
      <!-- 加载指示器 -->
      <div id="loading-indicator" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-family: Arial, sans-serif;
        color: #666;
        z-index: 9999;
      ">
        <div style="font-size: 18px; margin-bottom: 10px;">🔗 AstrBot 集成模式</div>
        <div style="font-size: 14px;">正在加载仪表盘...</div>
        <div style="margin-top: 10px;">
          <div style="
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
          "></div>
        </div>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>

      <script>
        // 5秒后隐藏加载指示器
        setTimeout(function() {
          const indicator = document.getElementById('loading-indicator');
          if (indicator) {
            indicator.style.display = 'none';
          }
        }, 5000);
      </script>
    </div>

  </body>
</html>
