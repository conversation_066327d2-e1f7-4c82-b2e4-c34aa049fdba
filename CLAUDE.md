# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

MaiBotOneKey 是麦麦MaiBot的一键包附加脚本存放仓库，包含了完整的MaiBot智能聊天机器人系统及其相关组件。

### 核心组件

- **MaiBot**: 基于大语言模型的智能聊天机器人核心
- **MaiBot-Napcat-Adapter**: MaiBot与NapCat的适配器，处理QQ消息收发
- **NapCat**: 基于NTQQ的Bot协议端实现
- **配置管理系统**: 自动化配置和初始化脚本

## 项目架构

### 目录结构
```
MaiBotOneKey/
├── modules/
│   ├── MaiBot/                    # 主要机器人核心
│   │   ├── src/                   # 源代码
│   │   ├── config/                # 配置文件
│   │   ├── template/              # 配置模板
│   │   └── plugins/               # 插件系统
│   ├── MaiBot-Napcat-Adapter/     # QQ适配器
│   └── napcat/                    # NapCat协议端
├── runtime/                       # 运行时环境
│   └── python31211/              # 内置Python环境
├── config_manager.py              # 配置管理器
├── main.py                        # 主启动脚本
├── start.py                       # 启动脚本
└── update_modules.py              # 模块更新脚本
```

### 系统架构
1. **NapCat** 连接QQ客户端，接收和发送QQ消息
2. **MaiBot-Napcat-Adapter** 作为中间层，处理消息格式转换和路由
3. **MaiBot** 核心处理自然语言对话，包含记忆、人格、情感等系统
4. **配置管理系统** 提供自动化配置和初始化

## 常用命令

### 启动相关
```bash
# 首次运行（会执行完整初始化流程）
python main.py

# 非首次运行（跳过初始化）
python start.py

# 配置管理（交互式配置向导）
python config_manager.py

# 更新所有模块
python update_modules.py
```

### 开发和调试
```bash
# 进入MaiBot目录
cd modules/MaiBot

# 运行MaiBot核心（需要先配置）
python bot.py

# 运行适配器
cd ../MaiBot-Napcat-Adapter
python main.py
```

## 配置系统

### 主要配置文件
- `modules/MaiBot/config/bot_config.toml` - MaiBot主配置
- `modules/MaiBot/config/lpmm_config.toml` - LPMM配置
- `modules/MaiBot/.env` - 环境变量（API密钥等）
- `modules/MaiBot-Napcat-Adapter/config.toml` - 适配器配置

### 配置管理
- 使用 `config_manager.py` 进行交互式配置
- 支持8步配置流程：基本信息、人格、身份、表达方式、聊天模式、群聊权限、API密钥、高级设置
- 自动从模板创建配置文件
- 支持配置备份和恢复

## 核心功能

### MaiBot核心特性
- **智能对话系统**: 基于LLM的自然语言交互
- **持久记忆系统**: 基于图的长期记忆存储
- **动态人格系统**: 自适应的性格特征和表达方式
- **情感表达系统**: 丰富的表情包和情绪表达
- **插件系统**: 支持扩展功能

### 适配器功能
- WebSocket连接管理（NapCat ↔ MaiBot）
- 消息类型解析（文本、图片、混合消息、转发消息等）
- 消息队列处理
- 心跳检测和连接维护

## 开发注意事项

### 环境要求
- Python 3.10+
- 项目路径不能包含中文字符
- 需要有效的API密钥（如SiliconFlow）

### 代码规范
- 使用tomlkit处理TOML配置文件
- 使用loguru进行日志记录
- 遵循现有的错误处理模式
- 配置文件修改需要同时更新相关模板

### 调试和测试
- 查看日志文件：`logs/app_*.log.jsonl`
- 使用内置的诊断脚本：`comprehensive_diagnosis.py`
- 配置文件验证通过模板对比

### 安全考虑
- API密钥存储在.env文件中，不要提交到版本控制
- 路径检查防止中文字符导致的问题
- 配置文件备份机制防止配置丢失

## 常见问题

1. **首次运行失败**: 检查路径是否包含中文字符
2. **配置文件缺失**: 运行 `config_manager.py` 重新配置
3. **连接问题**: 检查NapCat和适配器的WebSocket连接状态
4. **API调用失败**: 验证.env文件中的API密钥是否正确

## 相关文档
- [官方文档](https://docs.mai-mai.org)
- [部署教程](https://docs.mai-mai.org/manual/deployment/mmc_deploy_windows.html)
- [适配器文档](https://docs.mai-mai.org/manual/adapters/napcat.html)